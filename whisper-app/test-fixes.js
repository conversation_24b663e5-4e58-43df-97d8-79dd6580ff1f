// Simple test to verify basic functionality
console.log('🧪 Testing application fixes...\n');

// Test 1: Check if files exist and are properly structured
console.log('1. Checking file structure...');
const fs = require('fs');
const path = require('path');

const filesToCheck = [
  'src/services/firebase.js',
  'src/services/whisperService.js',
  'src/hooks/useWhispers.js',
  'src/components/AudioPlayer.js',
  'src/components/AudioRecorder.js',
  'App.js'
];

filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} exists`);
  } else {
    console.log(`   ❌ ${file} missing`);
  }
});

// Test 2: Check package.json dependencies
console.log('\n2. Checking dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = ['firebase', 'expo-av', '@react-native-async-storage/async-storage'];

  requiredDeps.forEach(dep => {
    if (packageJson.dependencies[dep]) {
      console.log(`   ✅ ${dep} installed`);
    } else {
      console.log(`   ❌ ${dep} missing`);
    }
  });
} catch (error) {
  console.log(`   ❌ Error reading package.json: ${error.message}`);
}

console.log('\n🎉 Basic checks completed!');
console.log('\n📱 To test the app fully, run: npx expo start');
