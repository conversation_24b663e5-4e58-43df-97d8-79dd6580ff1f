#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "folly/AtomicHashArray-inl.h"
#import "folly/AtomicHashArray.h"
#import "folly/AtomicHashMap-inl.h"
#import "folly/AtomicHashMap.h"
#import "folly/AtomicIntrusiveLinkedList.h"
#import "folly/AtomicLinkedList.h"
#import "folly/AtomicUnorderedMap.h"
#import "folly/base64.h"
#import "folly/Benchmark.h"
#import "folly/BenchmarkUtil.h"
#import "folly/Bits.h"
#import "folly/CancellationToken-inl.h"
#import "folly/CancellationToken.h"
#import "folly/Chrono.h"
#import "folly/ClockGettimeWrappers.h"
#import "folly/ConcurrentBitSet.h"
#import "folly/ConcurrentLazy.h"
#import "folly/ConcurrentSkipList-inl.h"
#import "folly/ConcurrentSkipList.h"
#import "folly/ConstexprMath.h"
#import "folly/ConstructorCallbackList.h"
#import "folly/Conv.h"
#import "folly/CPortability.h"
#import "folly/CppAttributes.h"
#import "folly/CpuId.h"
#import "folly/DefaultKeepAliveExecutor.h"
#import "folly/Demangle.h"
#import "folly/DiscriminatedPtr.h"
#import "folly/dynamic-inl.h"
#import "folly/dynamic.h"
#import "folly/DynamicConverter.h"
#import "folly/Exception.h"
#import "folly/ExceptionString.h"
#import "folly/ExceptionWrapper-inl.h"
#import "folly/ExceptionWrapper.h"
#import "folly/Executor.h"
#import "folly/Expected.h"
#import "folly/FBString.h"
#import "folly/FBVector.h"
#import "folly/File.h"
#import "folly/FileUtil.h"
#import "folly/Fingerprint.h"
#import "folly/FixedString.h"
#import "folly/FollyMemcpy.h"
#import "folly/FollyMemset.h"
#import "folly/Format-inl.h"
#import "folly/Format.h"
#import "folly/FormatArg.h"
#import "folly/FormatTraits.h"
#import "folly/Function.h"
#import "folly/GLog.h"
#import "folly/GroupVarint.h"
#import "folly/Hash.h"
#import "folly/Indestructible.h"
#import "folly/IndexedMemPool.h"
#import "folly/IntrusiveList.h"
#import "folly/IPAddress.h"
#import "folly/IPAddressException.h"
#import "folly/IPAddressV4.h"
#import "folly/IPAddressV6.h"
#import "folly/json.h"
#import "folly/json_patch.h"
#import "folly/json_pointer.h"
#import "folly/Lazy.h"
#import "folly/Likely.h"
#import "folly/MacAddress.h"
#import "folly/MapUtil.h"
#import "folly/Math.h"
#import "folly/MaybeManagedPtr.h"
#import "folly/Memory.h"
#import "folly/MicroLock.h"
#import "folly/MicroSpinLock.h"
#import "folly/MoveWrapper.h"
#import "folly/MPMCPipeline.h"
#import "folly/MPMCQueue.h"
#import "folly/ObserverContainer.h"
#import "folly/Optional.h"
#import "folly/Overload.h"
#import "folly/PackedSyncPtr.h"
#import "folly/Padded.h"
#import "folly/Poly-inl.h"
#import "folly/Poly.h"
#import "folly/PolyException.h"
#import "folly/Portability.h"
#import "folly/Preprocessor.h"
#import "folly/ProducerConsumerQueue.h"
#import "folly/Random-inl.h"
#import "folly/Random.h"
#import "folly/Range.h"
#import "folly/Replaceable.h"
#import "folly/RWSpinLock.h"
#import "folly/ScopeGuard.h"
#import "folly/SharedMutex.h"
#import "folly/Singleton-inl.h"
#import "folly/Singleton.h"
#import "folly/SingletonThreadLocal.h"
#import "folly/small_vector.h"
#import "folly/SocketAddress.h"
#import "folly/sorted_vector_types.h"
#import "folly/SpinLock.h"
#import "folly/stop_watch.h"
#import "folly/String-inl.h"
#import "folly/String.h"
#import "folly/Subprocess.h"
#import "folly/Synchronized.h"
#import "folly/SynchronizedPtr.h"
#import "folly/ThreadCachedInt.h"
#import "folly/ThreadLocal.h"
#import "folly/TimeoutQueue.h"
#import "folly/TokenBucket.h"
#import "folly/Traits.h"
#import "folly/Try-inl.h"
#import "folly/Try.h"
#import "folly/Unicode.h"
#import "folly/Unit.h"
#import "folly/Uri-inl.h"
#import "folly/Uri.h"
#import "folly/UTF8String.h"
#import "folly/Utility.h"
#import "folly/Varint.h"
#import "folly/VirtualExecutor.h"
#import "folly/algorithm/simd/Contains.h"
#import "folly/algorithm/simd/FindFixed.h"
#import "folly/algorithm/simd/Ignore.h"
#import "folly/algorithm/simd/Movemask.h"
#import "folly/algorithm/simd/detail/ContainsImpl.h"
#import "folly/algorithm/simd/detail/SimdAnyOf.h"
#import "folly/algorithm/simd/detail/SimdForEach.h"
#import "folly/algorithm/simd/detail/SimdPlatform.h"
#import "folly/algorithm/simd/detail/Traits.h"
#import "folly/algorithm/simd/detail/UnrollUtils.h"
#import "folly/chrono/Clock.h"
#import "folly/chrono/Conv.h"
#import "folly/chrono/Hardware.h"
#import "folly/container/Access.h"
#import "folly/container/Array.h"
#import "folly/container/BitIterator.h"
#import "folly/container/Enumerate.h"
#import "folly/container/EvictingCacheMap.h"
#import "folly/container/F14Map-fwd.h"
#import "folly/container/F14Map.h"
#import "folly/container/F14Set-fwd.h"
#import "folly/container/F14Set.h"
#import "folly/container/FBVector.h"
#import "folly/container/Foreach-inl.h"
#import "folly/container/Foreach.h"
#import "folly/container/heap_vector_types.h"
#import "folly/container/HeterogeneousAccess-fwd.h"
#import "folly/container/HeterogeneousAccess.h"
#import "folly/container/IntrusiveHeap.h"
#import "folly/container/IntrusiveList.h"
#import "folly/container/Iterator.h"
#import "folly/container/MapUtil.h"
#import "folly/container/Merge.h"
#import "folly/container/range_traits.h"
#import "folly/container/RegexMatchCache.h"
#import "folly/container/Reserve.h"
#import "folly/container/small_vector.h"
#import "folly/container/sorted_vector_types.h"
#import "folly/container/span.h"
#import "folly/container/SparseByteSet.h"
#import "folly/container/tape.h"
#import "folly/container/View.h"
#import "folly/container/WeightedEvictingCacheMap.h"
#import "folly/container/detail/BitIteratorDetail.h"
#import "folly/container/detail/F14Defaults.h"
#import "folly/container/detail/F14IntrinsicsAvailability.h"
#import "folly/container/detail/F14MapFallback.h"
#import "folly/container/detail/F14Mask.h"
#import "folly/container/detail/F14Policy.h"
#import "folly/container/detail/F14SetFallback.h"
#import "folly/container/detail/F14Table.h"
#import "folly/container/detail/tape_detail.h"
#import "folly/container/detail/Util.h"
#import "folly/detail/AsyncTrace.h"
#import "folly/detail/AtomicHashUtils.h"
#import "folly/detail/AtomicUnorderedMapUtils.h"
#import "folly/detail/DiscriminatedPtrDetail.h"
#import "folly/detail/FileUtilDetail.h"
#import "folly/detail/FileUtilVectorDetail.h"
#import "folly/detail/FingerprintPolynomial.h"
#import "folly/detail/Futex-inl.h"
#import "folly/detail/Futex.h"
#import "folly/detail/GroupVarintDetail.h"
#import "folly/detail/IPAddress.h"
#import "folly/detail/IPAddressSource.h"
#import "folly/detail/Iterators.h"
#import "folly/detail/MemoryIdler.h"
#import "folly/detail/MPMCPipelineDetail.h"
#import "folly/detail/PerfScoped.h"
#import "folly/detail/PolyDetail.h"
#import "folly/detail/RangeCommon.h"
#import "folly/detail/RangeSse42.h"
#import "folly/detail/SimpleSimdStringUtils.h"
#import "folly/detail/SimpleSimdStringUtilsImpl.h"
#import "folly/detail/Singleton.h"
#import "folly/detail/SlowFingerprint.h"
#import "folly/detail/SocketFastOpen.h"
#import "folly/detail/SplitStringSimd.h"
#import "folly/detail/SplitStringSimdImpl.h"
#import "folly/detail/Sse.h"
#import "folly/detail/StaticSingletonManager.h"
#import "folly/detail/ThreadLocalDetail.h"
#import "folly/detail/thread_local_globals.h"
#import "folly/detail/TrapOnAvx512.h"
#import "folly/detail/TurnSequencer.h"
#import "folly/detail/TypeList.h"
#import "folly/detail/UniqueInstance.h"
#import "folly/functional/ApplyTuple.h"
#import "folly/functional/Invoke.h"
#import "folly/functional/Partial.h"
#import "folly/functional/protocol.h"
#import "folly/functional/traits.h"
#import "folly/hash/Checksum.h"
#import "folly/hash/FarmHash.h"
#import "folly/hash/Hash.h"
#import "folly/hash/MurmurHash.h"
#import "folly/hash/SpookyHashV1.h"
#import "folly/hash/SpookyHashV2.h"
#import "folly/hash/traits.h"
#import "folly/json/dynamic-inl.h"
#import "folly/json/dynamic.h"
#import "folly/json/DynamicConverter.h"
#import "folly/json/DynamicParser-inl.h"
#import "folly/json/DynamicParser.h"
#import "folly/json/json.h"
#import "folly/json/JsonMockUtil.h"
#import "folly/json/JSONSchema.h"
#import "folly/json/JsonTestUtil.h"
#import "folly/json/json_patch.h"
#import "folly/json/json_pointer.h"
#import "folly/lang/Access.h"
#import "folly/lang/Align.h"
#import "folly/lang/Aligned.h"
#import "folly/lang/Assume.h"
#import "folly/lang/Badge.h"
#import "folly/lang/Bits.h"
#import "folly/lang/BitsClass.h"
#import "folly/lang/Builtin.h"
#import "folly/lang/CArray.h"
#import "folly/lang/Cast.h"
#import "folly/lang/CheckedMath.h"
#import "folly/lang/CString.h"
#import "folly/lang/CustomizationPoint.h"
#import "folly/lang/Exception.h"
#import "folly/lang/Extern.h"
#import "folly/lang/Hint-inl.h"
#import "folly/lang/Hint.h"
#import "folly/lang/Keep.h"
#import "folly/lang/New.h"
#import "folly/lang/Ordering.h"
#import "folly/lang/Pretty.h"
#import "folly/lang/PropagateConst.h"
#import "folly/lang/RValueReferenceWrapper.h"
#import "folly/lang/SafeAssert.h"
#import "folly/lang/StaticConst.h"
#import "folly/lang/Thunk.h"
#import "folly/lang/ToAscii.h"
#import "folly/lang/TypeInfo.h"
#import "folly/lang/UncaughtExceptions.h"
#import "folly/memory/Arena-inl.h"
#import "folly/memory/Arena.h"
#import "folly/memory/JemallocHugePageAllocator.h"
#import "folly/memory/JemallocNodumpAllocator.h"
#import "folly/memory/MallctlHelper.h"
#import "folly/memory/Malloc.h"
#import "folly/memory/MemoryResource.h"
#import "folly/memory/not_null-inl.h"
#import "folly/memory/not_null.h"
#import "folly/memory/ReentrantAllocator.h"
#import "folly/memory/SanitizeAddress.h"
#import "folly/memory/SanitizeLeak.h"
#import "folly/memory/ThreadCachedArena.h"
#import "folly/memory/UninitializedMemoryHacks.h"
#import "folly/memory/detail/MallocImpl.h"
#import "folly/net/NetOps.h"
#import "folly/net/NetOpsDispatcher.h"
#import "folly/net/NetworkSocket.h"
#import "folly/net/TcpInfo.h"
#import "folly/net/TcpInfoDispatcher.h"
#import "folly/net/TcpInfoTypes.h"
#import "folly/net/detail/SocketFileDescriptorMap.h"
#import "folly/portability/Asm.h"
#import "folly/portability/Atomic.h"
#import "folly/portability/Builtins.h"
#import "folly/portability/Config.h"
#import "folly/portability/Constexpr.h"
#import "folly/portability/Dirent.h"
#import "folly/portability/Event.h"
#import "folly/portability/Fcntl.h"
#import "folly/portability/Filesystem.h"
#import "folly/portability/FmtCompile.h"
#import "folly/portability/GFlags.h"
#import "folly/portability/GMock.h"
#import "folly/portability/GTest.h"
#import "folly/portability/IOVec.h"
#import "folly/portability/Libgen.h"
#import "folly/portability/Libunwind.h"
#import "folly/portability/Malloc.h"
#import "folly/portability/Math.h"
#import "folly/portability/Memory.h"
#import "folly/portability/openat2.h"
#import "folly/portability/OpenSSL.h"
#import "folly/portability/PThread.h"
#import "folly/portability/Sched.h"
#import "folly/portability/Sockets.h"
#import "folly/portability/SourceLocation.h"
#import "folly/portability/Stdio.h"
#import "folly/portability/Stdlib.h"
#import "folly/portability/String.h"
#import "folly/portability/SysFile.h"
#import "folly/portability/Syslog.h"
#import "folly/portability/SysMembarrier.h"
#import "folly/portability/SysMman.h"
#import "folly/portability/SysResource.h"
#import "folly/portability/SysStat.h"
#import "folly/portability/SysSyscall.h"
#import "folly/portability/SysTime.h"
#import "folly/portability/SysTypes.h"
#import "folly/portability/SysUio.h"
#import "folly/portability/Time.h"
#import "folly/portability/Unistd.h"
#import "folly/portability/Windows.h"
#import "folly/system/AtFork.h"
#import "folly/system/AuxVector.h"
#import "folly/system/EnvUtil.h"
#import "folly/system/HardwareConcurrency.h"
#import "folly/system/MemoryMapping.h"
#import "folly/system/Pid.h"
#import "folly/system/Shell.h"
#import "folly/system/ThreadId.h"
#import "folly/system/ThreadName.h"
#import "folly/concurrency/CacheLocality.h"
#import "folly/synchronization/AsymmetricThreadFence.h"
#import "folly/synchronization/AtomicNotification-inl.h"
#import "folly/synchronization/AtomicNotification.h"
#import "folly/synchronization/AtomicRef.h"
#import "folly/synchronization/AtomicStruct.h"
#import "folly/synchronization/AtomicUtil-inl.h"
#import "folly/synchronization/AtomicUtil.h"
#import "folly/synchronization/Baton.h"
#import "folly/synchronization/CallOnce.h"
#import "folly/synchronization/DelayedInit.h"
#import "folly/synchronization/DistributedMutex-inl.h"
#import "folly/synchronization/DistributedMutex.h"
#import "folly/synchronization/EventCount.h"
#import "folly/synchronization/FlatCombining.h"
#import "folly/synchronization/Hazptr-fwd.h"
#import "folly/synchronization/Hazptr.h"
#import "folly/synchronization/HazptrDomain.h"
#import "folly/synchronization/HazptrHolder.h"
#import "folly/synchronization/HazptrObj.h"
#import "folly/synchronization/HazptrObjLinked.h"
#import "folly/synchronization/HazptrRec.h"
#import "folly/synchronization/HazptrThreadPoolExecutor.h"
#import "folly/synchronization/HazptrThrLocal.h"
#import "folly/synchronization/Latch.h"
#import "folly/synchronization/LifoSem.h"
#import "folly/synchronization/Lock.h"
#import "folly/synchronization/MicroSpinLock.h"
#import "folly/synchronization/NativeSemaphore.h"
#import "folly/synchronization/ParkingLot.h"
#import "folly/synchronization/PicoSpinLock.h"
#import "folly/synchronization/Rcu.h"
#import "folly/synchronization/RelaxedAtomic.h"
#import "folly/synchronization/RWSpinLock.h"
#import "folly/synchronization/SanitizeThread.h"
#import "folly/synchronization/SaturatingSemaphore.h"
#import "folly/synchronization/SmallLocks.h"
#import "folly/synchronization/ThrottledLifoSem.h"
#import "folly/synchronization/WaitOptions.h"
#import "folly/system/ThreadId.h"

FOUNDATION_EXPORT double follyVersionNumber;
FOUNDATION_EXPORT const unsigned char follyVersionString[];

