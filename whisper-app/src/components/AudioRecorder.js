import { useState, useRef } from 'react';
import { Alert } from 'react-native';
import { Audio } from 'expo-av';
import * as MediaLibrary from 'expo-media-library';

const useAudioRecorder = ({ onRecordingComplete, onRecordingStart, onRecordingStop }) => {
  const [recording, setRecording] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingRef = useRef(null);
  const durationInterval = useRef(null);

  console.log('🎙️ useAudioRecorder initialized with callbacks:', {
    onRecordingComplete: !!onRecordingComplete,
    onRecordingStart: !!onRecordingStart,
    onRecordingStop: !!onRecordingStop
  });

  const startRecording = async () => {
    try {
      console.log('🎙️ Starting recording process...');

      // Request permissions
      const { status: audioStatus } = await Audio.requestPermissionsAsync();
      console.log('🎙️ Audio permission status:', audioStatus);

      if (audioStatus !== 'granted') {
        console.log('❌ Audio permission denied');
        Alert.alert('Permission required', 'Please grant audio recording permissions');
        return;
      }

      // Configure audio mode
      console.log('🎙️ Configuring audio mode...');
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Start recording
      console.log('🎙️ Creating recording...');
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);
      recordingRef.current = newRecording;

      console.log('✅ Recording started successfully');

      // Start duration counter
      durationInterval.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          // Auto-stop at 30 seconds
          if (newDuration >= 30) {
            console.log('⏰ Auto-stopping recording at 30 seconds');
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);

      onRecordingStart && onRecordingStart();
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      console.error('Error details:', error.message);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    try {
      console.log('🛑 Stopping recording...');

      if (!recording) {
        console.log('⚠️ No recording to stop');
        return;
      }

      setIsRecording(false);
      clearInterval(durationInterval.current);

      console.log('🛑 Stopping and unloading recording...');
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      console.log('✅ Recording stopped, URI:', uri);

      setRecording(null);
      recordingRef.current = null;

      onRecordingStop && onRecordingStop();

      console.log('🎙️ Calling onRecordingComplete with URI:', uri);
      console.log('🎙️ onRecordingComplete callback exists:', !!onRecordingComplete);

      if (onRecordingComplete) {
        onRecordingComplete(uri);
        console.log('✅ onRecordingComplete called successfully');
      } else {
        console.log('❌ onRecordingComplete callback is missing!');
      }
    } catch (error) {
      console.error('❌ Failed to stop recording:', error);
      console.error('Error details:', error.message);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    startRecording,
    stopRecording,
    isRecording,
    recordingDuration,
    formatDuration,
  };
};

export default useAudioRecorder;
