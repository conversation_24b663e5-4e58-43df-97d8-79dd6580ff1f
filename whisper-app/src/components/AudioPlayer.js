import { useState, useRef, useEffect } from 'react';
import { Alert } from 'react-native';
import { Audio } from 'expo-av';

const useAudioPlayer = ({ audioUrl, onPlaybackStart, onPlaybackEnd }) => {
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(0);
  const soundRef = useRef(null);

  useEffect(() => {
    return () => {
      // Cleanup sound when component unmounts
      if (soundRef.current) {
        soundRef.current.unloadAsync();
      }
    };
  }, []);

  const playAudio = async () => {
    try {
      if (isPlaying) {
        await stopAudio();
        return;
      }

      if (!audioUrl) {
        Alert.alert('Error', 'No audio URL provided');
        return;
      }

      setIsLoading(true);

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Load and play sound
      console.log('🎵 Attempting to play audio:', audioUrl);
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: audioUrl },
        { shouldPlay: true },
        onPlaybackStatusUpdate
      );

      setSound(newSound);
      soundRef.current = newSound;
      setIsPlaying(true);
      setIsLoading(false);
      console.log('✅ Audio playback started successfully');

      onPlaybackStart && onPlaybackStart();
    } catch (error) {
      console.error('❌ Error playing audio:', error);
      console.error('Audio URL that failed:', audioUrl);
      setIsLoading(false);

      // More user-friendly error message
      if (error.message.includes('-1100') || error.message.includes('NSURLErrorDomain')) {
        Alert.alert('Audio Error', 'This audio file is not available. Please try another whisper.');
      } else {
        Alert.alert('Error', 'Failed to play audio. Please try again.');
      }
    }
  };

  const stopAudio = async () => {
    try {
      if (soundRef.current) {
        await soundRef.current.stopAsync();
        await soundRef.current.unloadAsync();
        soundRef.current = null;
      }

      setSound(null);
      setIsPlaying(false);
      setPosition(0);

      onPlaybackEnd && onPlaybackEnd();
    } catch (error) {
      console.error('Error stopping audio:', error);
    }
  };

  const onPlaybackStatusUpdate = (status) => {
    if (status.isLoaded) {
      setDuration(status.durationMillis || 0);
      setPosition(status.positionMillis || 0);

      if (status.didJustFinish) {
        setIsPlaying(false);
        setPosition(0);
        onPlaybackEnd && onPlaybackEnd();
      }
    }
  };

  const formatTime = (milliseconds) => {
    const seconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    if (duration === 0) return 0;
    return position / duration;
  };

  return {
    playAudio,
    stopAudio,
    isPlaying,
    isLoading,
    duration,
    position,
    formatTime,
    getProgress,
  };
};

export default useAudioPlayer;
