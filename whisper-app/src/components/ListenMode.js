import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  PanResponder,
  Dimensions,
  TouchableOpacity,
  Vibration,
} from 'react-native';
import useAudioPlayer from './AudioPlayer';
import { colors, fonts, spacing, borderRadius, shadows } from '../styles/theme';

const { width } = Dimensions.get('window');
const SWIPE_THRESHOLD = width * 0.2; // Reduced from 30% to 20% for faster triggering
const MIN_SWIPE_DISTANCE = 10; // Reduced from 15 for better responsiveness
const SWIPE_VELOCITY_THRESHOLD = 0.5; // Add velocity threshold for quick swipes

const ListenMode = ({
  whisper,
  onSwipeLeft,
  onSwipeRight,
  canSwipeLeft,
  canSwipeRight,
  whisperIndex,
  totalWhispers,
  isLoading
}) => {
  const [swipeDirection, setSwipeDirection] = useState(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [isSwipeInProgress, setIsSwipeInProgress] = useState(false); // Prevent multiple swipes

  // Simple pan gesture state without animations
  const [panX, setPanX] = useState(0);

  const audioPlayer = useAudioPlayer({
    audioUrl: whisper?.audioUrl,
    onPlaybackStart: () => {
      console.log('🎵 ListenMode: Audio playback started');
    },
    onPlaybackEnd: () => {
      console.log('🎵 ListenMode: Audio playback ended');
    },
  });

  // Reset pan when whisper changes
  useEffect(() => {
    setPanX(0);
    setSwipeDirection(null);
    setIsSwipeActive(false);
    setIsSwipeInProgress(false);
  }, [whisper?.id]);

  // Removed loading animation to avoid conflicts

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // Only respond to horizontal swipes and not when audio is loading or swipe in progress
        const isHorizontal = Math.abs(gestureState.dx) > Math.abs(gestureState.dy);
        const isSignificant = Math.abs(gestureState.dx) > MIN_SWIPE_DISTANCE;
        const hasVelocity = Math.abs(gestureState.vx) > 0.1; // Detect quick swipes
        return isHorizontal && (isSignificant || hasVelocity) && !audioPlayer.isLoading && !isSwipeInProgress;
      },
      onStartShouldSetPanResponder: () => false, // Don't capture on start
      onStartShouldSetPanResponderCapture: () => false,
      onPanResponderGrant: () => {
        setIsSwipeActive(true);
        // Light haptic feedback when starting swipe
        Vibration.vibrate(10);
        setPanX(0);
      },
      onPanResponderMove: (evt, gestureState) => {
        // Determine swipe direction and check if it's allowed
        let direction = null;
        let isAllowed = false;

        if (gestureState.dx > 0 && canSwipeRight) {
          direction = 'right';
          isAllowed = true;
        } else if (gestureState.dx < 0 && canSwipeLeft) {
          direction = 'left';
          isAllowed = true;
        }

        setSwipeDirection(direction);

        if (isAllowed) {
          // Apply movement with resistance for responsive feel
          const resistance = 0.8;
          setPanX(gestureState.dx * resistance);
        } else {
          // If not allowed, provide subtle resistance feedback
          const resistance = 0.2;
          setPanX(gestureState.dx * resistance);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        setIsSwipeActive(false);

        // Check for swipe trigger based on distance OR velocity
        const swipeDistance = Math.abs(gestureState.dx);
        const swipeVelocity = Math.abs(gestureState.vx);
        const shouldTriggerSwipe = swipeDistance > SWIPE_THRESHOLD || swipeVelocity > SWIPE_VELOCITY_THRESHOLD;

        const direction = gestureState.dx > 0 ? 'right' : 'left';
        const isAllowed = (direction === 'right' && canSwipeRight) || (direction === 'left' && canSwipeLeft);

        if (shouldTriggerSwipe && isAllowed && !isSwipeInProgress) {
          setIsSwipeInProgress(true);

          // Success haptic feedback
          Vibration.vibrate(50);

          // Immediate trigger without animation
          setPanX(0);
          setIsSwipeInProgress(false);

          if (direction === 'right') {
            onSwipeRight && onSwipeRight();
          } else {
            onSwipeLeft && onSwipeLeft();
          }
        } else {
          // Reset to center
          setPanX(0);
        }

        setSwipeDirection(null);
      },
    })
  ).current;

  const getSwipeIndicatorText = () => {
    if (swipeDirection === 'left') {
      return '👈 Ďalší šepot';
    } else if (swipeDirection === 'right') {
      return 'Predchádzajúci šepot 👉';
    }
    return '';
  };

  const getSwipeIndicatorStyle = () => {
    const baseStyle = [styles.swipeIndicator];

    if (swipeDirection === 'left') {
      return [...baseStyle, styles.swipeIndicatorLeft, { opacity: 0.8 }];
    } else if (swipeDirection === 'right') {
      return [...baseStyle, styles.swipeIndicatorRight, { opacity: 0.8 }];
    }
    return [...baseStyle, { opacity: 0 }];
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Načítavam šepot...</Text>
          <Text style={styles.loadingDot}>⏳</Text>
        </View>
      </View>
    );
  }

  if (!whisper) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            Žiadne šepoty nie sú dostupné.
          </Text>
          <Text style={styles.emptySubtext}>
            Skúste neskôr alebo nahrajte vlastný šepot! 🎙️
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Swipe Indicators */}
      <View style={getSwipeIndicatorStyle()}>
        <Text style={styles.swipeIndicatorText}>
          {getSwipeIndicatorText()}
        </Text>
      </View>

      {/* Main Content */}
      <View
        style={[
          styles.content,
          {
            marginLeft: panX,
          },
        ]}
        {...panResponder.panHandlers}
      >
        {/* Top Section - Counter */}
        <View style={styles.topSection}>
          {totalWhispers > 0 && (
            <View style={styles.counterContainer}>
              <Text style={styles.counterText}>
                {whisperIndex + 1} / {totalWhispers}
              </Text>
            </View>
          )}
        </View>

        {/* Middle Section - Play Button and Progress */}
        <View style={styles.middleSection}>
          <TouchableOpacity
            style={[
              styles.playButton,
              audioPlayer.isPlaying && styles.playButtonActive,
              audioPlayer.isLoading && styles.playButtonLoading,
            ]}
            onPress={audioPlayer.playAudio}
            disabled={audioPlayer.isLoading}
          >
            <Text style={styles.playIcon}>
              {audioPlayer.isLoading ? '⏳' : audioPlayer.isPlaying ? '⏸️' : '▶️'}
            </Text>
          </TouchableOpacity>

          {/* Progress Bar */}
          {audioPlayer.isPlaying && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${audioPlayer.getProgress() * 100}%` },
                  ]}
                />
              </View>
              <Text style={styles.timeText}>
                {audioPlayer.formatTime(audioPlayer.position)} / {audioPlayer.formatTime(audioPlayer.duration)}
              </Text>
            </View>
          )}

          {/* Status Text */}
          <Text style={styles.statusText}>
            {audioPlayer.isPlaying
              ? 'Prehrávam anonymný šepot...'
              : 'Kliknite pre prehranie šepotu'
            }
          </Text>
        </View>

        {/* Bottom Section - Swipe Hints */}
        <View style={styles.bottomSection}>
          {!isSwipeActive && !audioPlayer.isPlaying && !isSwipeInProgress && (
            <View style={styles.hintsContainer}>
              {canSwipeRight && (
                <View style={[styles.hintItem, styles.hintItemLeft]}>
                  <Text style={styles.hintText}>👉 Predchádzajúci</Text>
                </View>
              )}
              {canSwipeLeft && (
                <View style={[styles.hintItem, styles.hintItemRight]}>
                  <Text style={styles.hintText}>Ďalší 👈</Text>
                </View>
              )}
            </View>
          )}

          {/* Show swipe progress indicator */}
          {isSwipeActive && (
            <View style={styles.swipeProgressContainer}>
              <Text style={styles.swipeProgressText}>
                {swipeDirection === 'left' ? 'Ťahajte pre ďalší šepot' : 'Ťahajte pre predchádzajúci šepot'}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    width: '100%',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: 120, // Fixed space for app title (60px) + extra margin
  },
  topSection: {
    flex: 0.15,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: spacing.md,
  },
  middleSection: {
    flex: 0.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSection: {
    flex: 0.15,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: spacing.md,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    color: colors.lightGray,
    fontSize: fonts.size.large,
    marginBottom: spacing.md,
  },
  loadingDots: {
    flexDirection: 'row',
  },
  loadingDot: {
    color: colors.neonPurple,
    fontSize: fonts.size.large,
    marginHorizontal: spacing.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyText: {
    color: colors.lightGray,
    fontSize: fonts.size.large,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
    opacity: 0.7,
  },
  playButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.deepPurple,
    borderWidth: 3,
    borderColor: colors.neonPurple,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    ...shadows.glow,
    elevation: 8,
    shadowColor: colors.neonPurple,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  playButtonActive: {
    backgroundColor: colors.neonPurple,
    borderColor: colors.white,
    shadowOpacity: 0.5,
    shadowRadius: 12,
  },
  playButtonLoading: {
    opacity: 0.7,
  },
  playIcon: {
    fontSize: 40,
    color: colors.white,
  },
  progressContainer: {
    width: '80%',
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.darkGray,
    borderRadius: 3,
    marginBottom: spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.neonPurple,
    borderRadius: 3,
  },
  timeText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    textAlign: 'center',
  },
  statusText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
    marginTop: spacing.md,
    opacity: 0.8,
  },
  counterContainer: {
    alignItems: 'center',
  },
  counterText: {
    color: colors.neonPurple,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  hintsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: spacing.md,
  },
  hintItem: {
    backgroundColor: colors.darkGray,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    opacity: 0.8,
    borderWidth: 1,
    borderColor: colors.neonPurple,
  },
  hintItemLeft: {
    marginRight: spacing.sm,
  },
  hintItemRight: {
    marginLeft: spacing.sm,
  },
  hintText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    fontWeight: '500',
  },
  swipeProgressContainer: {
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  swipeProgressText: {
    color: colors.neonPurple,
    fontSize: fonts.size.small,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  swipeIndicator: {
    position: 'absolute',
    top: '40%',
    zIndex: 10,
    backgroundColor: colors.neonPurple,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderRadius: borderRadius.xl,
    ...shadows.glow,
    elevation: 15,
    shadowColor: colors.neonPurple,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.8,
    shadowRadius: 20,
    borderWidth: 2,
    borderColor: colors.white,
  },
  swipeIndicatorLeft: {
    right: spacing.xl,
  },
  swipeIndicatorRight: {
    left: spacing.xl,
  },
  swipeIndicatorText: {
    color: colors.white,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ListenMode;
