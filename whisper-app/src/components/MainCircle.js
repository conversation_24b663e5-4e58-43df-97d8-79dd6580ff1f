import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { colors, fonts, shadows } from '../styles/theme';

const { width, height } = Dimensions.get('window');
const CIRCLE_SIZE = 200;

const MainCircle = ({ mode, onPress, isActive, isRecording, isPlaying }) => {
  // Removed all animations to avoid conflicts

  // Removed animation effects to avoid conflicts

  const getCircleStyle = () => {
    let backgroundColor = colors.deepPurple;
    let borderColor = colors.neonPurple;

    if (mode === 'record' && isRecording) {
      backgroundColor = colors.neonPurple;
      borderColor = colors.white;
    } else if (mode === 'listen' && isPlaying) {
      backgroundColor = colors.mediumPurple;
      borderColor = colors.brightPurple;
    } else if (mode === 'browse') {
      backgroundColor = colors.lightPurple;
      borderColor = colors.neonPurple;
    }

    return {
      backgroundColor,
      borderColor,
    };
  };

  const getModeText = () => {
    switch (mode) {
      case 'record':
        return isRecording ? 'Nahrávam...' : 'Kliknite pre nahranie';
      case 'listen':
        return isPlaying ? 'Prehrávam...' : 'Kliknite pre počúvanie';
      case 'browse':
        return 'Prehliadať šepoty';
      default:
        return 'Šepot';
    }
  };

  const getModeIcon = () => {
    switch (mode) {
      case 'record':
        return '🎙️';
      case 'listen':
        return '👂';
      case 'browse':
        return '📚';
      default:
        return '👻';
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.glowCircle, { opacity: 0.3 }]} />
      <View style={[styles.circle, getCircleStyle()]}>
        <TouchableOpacity
          style={styles.touchable}
          onPress={onPress}
          activeOpacity={0.8}
        >
          <Text style={styles.icon}>{getModeIcon()}</Text>
          <Text style={styles.modeText}>{getModeText()}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  glowCircle: {
    position: 'absolute',
    width: CIRCLE_SIZE + 40,
    height: CIRCLE_SIZE + 40,
    borderRadius: (CIRCLE_SIZE + 40) / 2,
    backgroundColor: colors.neonPurple,
    opacity: 0.3,
  },
  circle: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    borderRadius: CIRCLE_SIZE / 2,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.glow,
  },
  touchable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    fontSize: 40,
    marginBottom: 8,
  },
  modeText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
    textAlign: 'center',
    opacity: 0.9,
  },
});

export default MainCircle;
