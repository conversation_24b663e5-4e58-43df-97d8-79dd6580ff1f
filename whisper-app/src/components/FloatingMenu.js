import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import { colors, fonts, spacing, borderRadius, shadows } from '../styles/theme';

const { width, height } = Dimensions.get('window');

const FloatingMenu = ({ onModeChange, currentMode }) => {
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [settingsFadeAnim] = useState(new Animated.Value(0));

  const modes = [
    { key: 'record', icon: '🎙️', label: 'Nahrať' },
    { key: 'listen', icon: '👂', label: 'Počúvať' },
    { key: 'browse', icon: '📚', label: 'Prehliadať' },
  ];

  const openModeSelector = () => {
    setShowModeSelector(true);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeModeSelector = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowModeSelector(false);
    });
  };

  const selectMode = (mode) => {
    onModeChange(mode);
    closeModeSelector();
  };

  const openSettings = () => {
    setShowSettings(true);
    Animated.timing(settingsFadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSettings = () => {
    Animated.timing(settingsFadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowSettings(false);
    });
  };

  return (
    <>
      {/* Floating Buttons */}
      <View style={styles.floatingContainer}>
        {/* Mode Selector Button */}
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={openModeSelector}
        >
          <Text style={styles.floatingIcon}>🎭</Text>
        </TouchableOpacity>

        {/* Settings Button */}
        <TouchableOpacity
          style={[styles.floatingButton, styles.settingsButton]}
          onPress={openSettings}
        >
          <Text style={styles.floatingIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* Mode Selector Modal */}
      <Modal
        visible={showModeSelector}
        transparent={true}
        animationType="none"
        onRequestClose={closeModeSelector}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeModeSelector}
        >
          <Animated.View
            style={[
              styles.modalContent,
              {
                opacity: fadeAnim,
                transform: [
                  {
                    scale: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text style={styles.modalTitle}>Vyberte režim</Text>
            
            {modes.map((mode) => (
              <TouchableOpacity
                key={mode.key}
                style={[
                  styles.modeOption,
                  currentMode === mode.key && styles.modeOptionActive,
                ]}
                onPress={() => selectMode(mode.key)}
              >
                <Text style={styles.modeIcon}>{mode.icon}</Text>
                <Text style={styles.modeLabel}>{mode.label}</Text>
                {currentMode === mode.key && (
                  <Text style={styles.activeIndicator}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
          </Animated.View>
        </TouchableOpacity>
      </Modal>

      {/* Settings Modal */}
      <Modal
        visible={showSettings}
        transparent={true}
        animationType="none"
        onRequestClose={closeSettings}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeSettings}
        >
          <Animated.View
            style={[
              styles.modalContent,
              {
                opacity: settingsFadeAnim,
                transform: [
                  {
                    scale: settingsFadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text style={styles.modalTitle}>Nastavenia</Text>

            <View style={styles.settingItem}>
              <Text style={styles.settingIcon}>👻</Text>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Verzia aplikácie</Text>
                <Text style={styles.settingValue}>1.0.0</Text>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingIcon}>🎙️</Text>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Kvalita zvuku</Text>
                <Text style={styles.settingValue}>Vysoká kvalita</Text>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingIcon}>🔥</Text>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Firebase</Text>
                <Text style={styles.settingValue}>Pripojené</Text>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingIcon}>💜</Text>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Téma</Text>
                <Text style={styles.settingValue}>Fialová tmavá</Text>
              </View>
            </View>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={closeSettings}
            >
              <Text style={styles.closeButtonText}>Zavrieť</Text>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  floatingContainer: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    zIndex: 1000,
  },
  floatingButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.deepPurple,
    borderWidth: 2,
    borderColor: colors.neonPurple,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.glow,
  },
  settingsButton: {
    backgroundColor: colors.darkGray,
    borderColor: colors.lightGray,
  },
  floatingIcon: {
    fontSize: 24,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.deepPurple,
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    borderColor: colors.neonPurple,
    padding: spacing.xl,
    width: width * 0.8,
    maxWidth: 300,
    ...shadows.glow,
  },
  modalTitle: {
    color: colors.white,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  modeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
    backgroundColor: colors.darkGray,
  },
  modeOptionActive: {
    backgroundColor: colors.neonPurple,
  },
  modeIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  modeLabel: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
    flex: 1,
  },
  activeIndicator: {
    color: colors.white,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
    backgroundColor: colors.darkGray,
  },
  settingIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingLabel: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
  },
  settingValue: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    marginTop: spacing.xs,
  },
  closeButton: {
    backgroundColor: colors.neonPurple,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: borderRadius.md,
    marginTop: spacing.lg,
    alignItems: 'center',
  },
  closeButtonText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
  },
});

export default FloatingMenu;
