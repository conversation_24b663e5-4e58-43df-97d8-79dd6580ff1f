import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  query,
  orderBy,
  limit,
  updateDoc,
  doc,
  where,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from './firebase';

// Test Firebase connection
export const testFirebaseConnection = async () => {
  try {
    console.log('Testing Firebase connection...');
    const testQuery = query(collection(db, 'whispers'), limit(1));
    const snapshot = await getDocs(testQuery);
    console.log('Firebase connection test successful, docs count:', snapshot.size);
    return true;
  } catch (error) {
    console.error('Firebase connection test failed:', error);
    return false;
  }
};

// Add test whispers to Firebase (for development)
export const addTestWhispers = async () => {
  try {
    console.log('🌱 Adding test whispers to Firebase...');

    const testWhispers = [
      {
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        userId: 'test_user_1',
        timestamp: serverTimestamp(),
        likes: 0,
        reports: 0,
        isActive: true
      }
    ];

    for (const whisperData of testWhispers) {
      const docRef = await addDoc(collection(db, 'whispers'), whisperData);
      console.log('✅ Test whisper added with ID:', docRef.id);
    }

    console.log('🎉 Test whispers added successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error adding test whispers:', error);
    return false;
  }
};

// Upload audio file to Firebase Storage
export const uploadAudio = async (audioUri, userId) => {
  try {
    console.log('Uploading audio:', { audioUri, userId });

    const response = await fetch(audioUri);
    const blob = await response.blob();
    console.log('Audio blob size:', blob.size);

    const fileName = `whispers/${userId}_${Date.now()}.m4a`;
    const storageRef = ref(storage, fileName);
    console.log('Storage path:', fileName);

    const snapshot = await uploadBytes(storageRef, blob);
    console.log('Upload completed, getting download URL...');

    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Download URL:', downloadURL);

    return downloadURL;
  } catch (error) {
    console.error('Error uploading audio:', error);
    console.error('Error details:', error.message);
    throw error;
  }
};

// Create new whisper in Firestore
export const createWhisper = async (audioUrl, userId) => {
  try {
    console.log('Creating whisper with data:', { audioUrl, userId });

    const whisperData = {
      audioUrl,
      userId,
      timestamp: serverTimestamp(),
      likes: 0,
      reports: 0,
      isActive: true
    };

    console.log('Whisper data to save:', whisperData);

    const docRef = await addDoc(collection(db, 'whispers'), whisperData);
    console.log('Whisper created with ID:', docRef.id);

    return docRef.id;
  } catch (error) {
    console.error('Error creating whisper:', error);
    console.error('Error details:', error.message);
    throw error;
  }
};

// Mock whispers for testing when Firebase is down
const mockWhispers = [
  {
    id: 'mock1',
    audioUrl: 'https://commondatastorage.googleapis.com/codeskulptor-demos/DDR_assets/Kangaroo_MusiQue_-_The_Neverwritten_Role_Playing_Game.mp3',
    userId: 'mock_user_1',
    timestamp: new Date(),
    likes: 5,
    reports: 0,
    isActive: true
  },
  {
    id: 'mock2',
    audioUrl: 'https://commondatastorage.googleapis.com/codeskulptor-assets/week7-brrring.m4a',
    userId: 'mock_user_2',
    timestamp: new Date(),
    likes: 3,
    reports: 0,
    isActive: true
  },
  {
    id: 'mock3',
    audioUrl: 'https://commondatastorage.googleapis.com/codeskulptor-demos/GalaxyInvaders/theme_01.mp3',
    userId: 'mock_user_3',
    timestamp: new Date(),
    likes: 8,
    reports: 0,
    isActive: true
  }
];

// Get random whisper
export const getRandomWhisper = async (excludeUserId = null) => {
  try {
    console.log('🎧 Fetching whispers from Firebase...');

    // First try to get real data from Firebase
    try {
      // Simple query without orderBy to avoid index requirement
      const q = query(
        collection(db, 'whispers'),
        limit(50) // Get whispers without ordering
      );

      const querySnapshot = await getDocs(q);
      const whispers = [];

      console.log('📊 Total docs in Firebase query:', querySnapshot.size);

      querySnapshot.forEach((doc) => {
        const whisperData = { id: doc.id, ...doc.data() };
        console.log('📄 Firebase whisper found:', {
          id: whisperData.id,
          userId: whisperData.userId,
          isActive: whisperData.isActive,
          audioUrl: whisperData.audioUrl?.substring(0, 50) + '...'
        });

        // Filter for active whispers and exclude user's own whispers
        if (whisperData.isActive && (!excludeUserId || whisperData.userId !== excludeUserId)) {
          whispers.push(whisperData);
          console.log('✅ Whisper passed filter:', whisperData.id);
        } else {
          console.log('❌ Whisper filtered out:', {
            id: whisperData.id,
            isActive: whisperData.isActive,
            excludeUserId: excludeUserId,
            userId: whisperData.userId
          });
        }
      });

      console.log('🔍 Available Firebase whispers after filtering:', whispers.length);

      if (whispers.length > 0) {
        const randomIndex = Math.floor(Math.random() * whispers.length);
        const selectedWhisper = whispers[randomIndex];
        console.log('✅ Selected Firebase whisper:', selectedWhisper.id);
        return selectedWhisper;
      } else {
        console.log('⚠️ No Firebase whispers available, falling back to mock data');
      }
    } catch (firebaseError) {
      console.error('❌ Firebase error, falling back to mock data:', firebaseError);
    }

    // Fallback to mock data if Firebase fails or has no data
    console.log('📱 Using mock data as fallback...');
    // For mock data, don't exclude any whispers since they're just test data
    const availableMocks = mockWhispers.filter(w => w.isActive);

    console.log('🔍 Available mock whispers:', availableMocks.length);

    if (availableMocks.length === 0) {
      console.log('❌ No mock whispers available');
      return null;
    }

    const randomIndex = Math.floor(Math.random() * availableMocks.length);
    const selectedWhisper = availableMocks[randomIndex];
    console.log('✅ Selected mock whisper:', selectedWhisper.id);
    return selectedWhisper;

  } catch (error) {
    console.error('❌ Error getting random whisper:', error);
    return null;
  }
};

// Get recent whispers for browse mode
export const getRecentWhispers = async (limitCount = 20) => {
  try {
    console.log('📋 Fetching recent whispers from Firebase...');

    // First try to get real data from Firebase
    try {
      // Simple query without orderBy to avoid index requirement
      const q = query(
        collection(db, 'whispers'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const whispers = [];

      querySnapshot.forEach((doc) => {
        const whisperData = { id: doc.id, ...doc.data() };
        // Filter for active whispers only
        if (whisperData.isActive) {
          whispers.push(whisperData);
        }
      });

      // Sort by timestamp manually since we can't use orderBy in query
      whispers.sort((a, b) => {
        const aTime = a.timestamp?.toDate?.() || a.timestamp || new Date(0);
        const bTime = b.timestamp?.toDate?.() || b.timestamp || new Date(0);
        return bTime - aTime; // Descending order (newest first)
      });

      console.log('✅ Found', whispers.length, 'Firebase whispers');

      if (whispers.length > 0) {
        return whispers;
      } else {
        console.log('⚠️ No Firebase whispers found, falling back to mock data');
      }
    } catch (firebaseError) {
      console.error('❌ Firebase error in browse mode, falling back to mock data:', firebaseError);
    }

    // Fallback to mock data if Firebase fails or has no data
    console.log('📱 Using mock data for browse mode...');
    const sortedMocks = [...mockWhispers]
      .filter(w => w.isActive)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limitCount);

    console.log('✅ Returning', sortedMocks.length, 'mock whispers');
    return sortedMocks;

  } catch (error) {
    console.error('❌ Error getting recent whispers:', error);
    // Return mock data as last resort
    return mockWhispers.filter(w => w.isActive).slice(0, limitCount);
  }
};

// Like a whisper
export const likeWhisper = async (whisperId) => {
  try {
    const whisperRef = doc(db, 'whispers', whisperId);
    await updateDoc(whisperRef, {
      likes: increment(1)
    });
  } catch (error) {
    console.error('Error liking whisper:', error);
    throw error;
  }
};

// Report a whisper
export const reportWhisper = async (whisperId) => {
  try {
    const whisperRef = doc(db, 'whispers', whisperId);

    // Get current document to check report count
    const whisperDoc = await getDoc(whisperRef);
    if (whisperDoc.exists()) {
      const currentReports = whisperDoc.data().reports || 0;
      const newReports = currentReports + 1;

      await updateDoc(whisperRef, {
        reports: newReports,
        isActive: newReports < 3 // Deactivate if 3+ reports
      });
    }
  } catch (error) {
    console.error('Error reporting whisper:', error);
    throw error;
  }
};
