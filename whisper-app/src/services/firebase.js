import { initializeApp } from "firebase/app";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getStorage, connectStorageEmulator } from "firebase/storage";

const firebaseConfig = {
  apiKey: "AIzaSyBltGdrE2uyNy2Q9eek9YQsmVeB_fu2Gyc",
  authDomain: "whisper-app-production.firebaseapp.com",
  projectId: "whisper-app-production",
  storageBucket: "whisper-app-production.firebasestorage.app",
  messagingSenderId: "805775786566",
  appId: "1:805775786566:web:e7eb50f559ad5ffe798047",
  measurementId: "G-QXTNGL6FKJ"
};

let app;
let db;
let storage;

try {
  console.log('🔥 Initializing Firebase app...');
  app = initializeApp(firebaseConfig);

  console.log('🔥 Initializing Firestore...');
  db = getFirestore(app);

  console.log('🔥 Initializing Storage...');
  storage = getStorage(app);

  console.log('✅ Firebase initialized successfully');
} catch (error) {
  console.error('❌ Firebase initialization failed:', error);
  // Create mock objects to prevent app crashes
  db = null;
  storage = null;
}

export { db, storage };
